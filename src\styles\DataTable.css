/* Course Page Header */
.course-page-header {
  margin: 16px 0 8px; /* Reduced bottom margin for closer spacing between hr line and table */
  /*
   * Align h2 heading with AddCourse page h2 heading:
   * AddCourse back button: 16px height + 16px margin-bottom = 32px
   * Course page padding-top: 32px to match
   */
  padding-top: 48px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.course-page-header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* gap: 16px; */
}

.course-page-header h2 {
  margin: 0;
  font-size: var(--h2-size);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

.add-course-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 8px;
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.add-course-button:hover {
  background-color: var(--secondary-text-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.add-course-button .button-icon {
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
}

/* Responsive design for course header */
@media (max-width: 768px) {
  .course-page-header {
    margin: 12px 0 0px; /* Minimal bottom margin for mobile - tightest spacing */
    /*
     * Mobile alignment: AddCourse back button 16px height + 12px margin = 28px
     * Course page padding-top: 28px to match
     */
    padding-top: 28px;
  }

  .course-page-header .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .add-course-button {
    justify-content: center;
  }
}

/* Course-specific column styles for backward compatibility */
.course-title {
  min-width: 200px;
  max-width: 300px;
}

.course-title-content {
  display: flex;
  flex-direction: column;
}

.course-title-text {
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  line-height: 1.4;
}

.course-description {
  min-width: 200px;
  max-width: 300px;
  line-height: 1.4;
}

.course-original-price,
.course-offer-price {
  min-width: 100px;
  font-weight: var(--font-weight-medium);
  text-align: right;
}

.course-status {
  min-width: 100px;
}

.course-actions {
  min-width: 100px;
  text-align: center;
}

/* Data Table Page Layout */
.data-table-page {
  display: flex;
  flex-direction: column;
  gap: 0px; /* Removed gap for tighter spacing between header and table */
  padding: 0px 0 24px; /* Remove horizontal padding - handled by dashboard-main-content */
  max-width: 1400px;
  margin: 0 auto;
}

.data-table-header {
  padding: 2px 0 12px;
  margin: 0;
}

.data-table-header .header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.data-table-content {
  width: 100%;
}

.data-table-container {
  background-color: var(--bg-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px;
}

.data-table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  background-color: var(--bg-color);
  margin-top: 16px;
  border: 1px solid #ddd;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family-default);
  border: 1px solid #ddd;
}

.data-table thead {
  background-color: #F8F6F0;
  border-bottom: 2px solid #ddd;
}

.data-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: var(--font-weight-semibold);
  font-size: var(--body3-size);
  color: var(--text-color);
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  white-space: nowrap;
}

.data-table th:last-child {
  border-right: none;
}

.data-table tbody tr {
  border-bottom: 1px solid #ddd;
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
  background-color: #FDFCF8;
}

.data-table tbody tr:last-child {
  border-bottom: 1px solid #ddd;
}

.data-table td {
  padding: 16px 12px;
  font-size: var(--body3-size);
  color: var(--text-color);
  border-right: 1px solid #ddd;
  vertical-align: top;
}

.data-table td:last-child {
  border-right: none;
}

/* Generic column styles - can be overridden with custom classes */
.data-row {
  cursor: pointer;
}

/* Status badge styles - commonly used for status columns */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #E8F5E8;
  color: #2D5A2D;
}

.status-inactive {
  background-color: #FFF2E8;
  color: #8B4513;
}

.status-draft {
  background-color: #F0F0F0;
  color: #666666;
}

.status-unknown {
  background-color: #F5F5F5;
  color: var(--secondary-text-color);
}

/* Action button styles */
.data-actions {
  min-width: 100px;
  text-align: center;
}

.action-button {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--text-color);
  color: var(--bg-color);
  transform: translateY(-1px);
}

.view-button {
  border-color: #4A90E2;
  color: #4A90E2;
}

.view-button:hover {
  background-color: #4A90E2;
  color: white;
}

.data-table-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 4px;
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.retry-button:hover {
  opacity: 0.8;
}

.text-error {
  color: #D32F2F;
}

/* Responsive Design - Matches AddCourse page breakpoints */
@media (max-width: 1024px) {
  .data-table-container {
    padding: 24px;
  }

  .data-table th,
  .data-table td {
    padding: 12px 8px;
  }

  .course-page-header {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .data-table-container {
    padding: 16px;
  }

  .data-table-header {
    padding: 8px 0 12px;
  }

  .data-table-wrapper {
    font-size: var(--caption-size);
    border-radius: 8px;
    overflow-x: auto;
  }

  .data-table {
    min-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 10px 8px;
  }

  .course-page-header {
    margin-bottom: 20px;
  }

  .course-page-header .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .add-course-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .data-table-container {
    padding: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 6px;
    font-size: 13px;
  }

  .course-page-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }

  .data-table {
    font-size: 13px;
  }

  .action-button {
    padding: 4px 8px;
    font-size: 12px;
  }

  .status-badge {
    padding: 2px 6px;
    font-size: 11px;
  }
}
