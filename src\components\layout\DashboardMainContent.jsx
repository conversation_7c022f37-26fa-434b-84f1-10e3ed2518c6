import React from 'react';
import '../../styles/DashboardMainContent.css';

const DashboardMainContent = ({
  activeTab
}) => {
  const renderHomeContent = () => {
    return (
      <div className="dashboard-home">
        <div className="dashboard-welcome">
          <h2 className="h2 text-primary">Welcome to Admin Portal</h2>
          <p className="body2 text-secondary">
            Manage your courses, categories, and other administrative tasks from here.
          </p>
        </div>

        <div className="dashboard-stats">
          <div className="stat-card">
            <h3 className="h3 text-primary">Quick Actions</h3>
            <p className="body3 text-secondary">
              Use the sidebar to navigate to different sections:
            </p>
            <ul className="action-list">
              <li>📚 Courses - Manage course content and settings</li>
              <li>👤 Profile - Update your account information</li>
            </ul>
          </div>
        </div>
      </div>
    );
  };



  const renderCategoriesContent = () => (
    <div className="categories-section">
      <h2 className="h2 text-primary">Categories</h2>
      <p className="body2 text-secondary">Categories content coming soon...</p>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return renderHomeContent();
      case 'categories':
        return renderCategoriesContent();
      default:
        return renderHomeContent();
    }
  };

  return (
    <div className="dashboard-main-content">
      <div className="content-container">
        {renderContent()}
      </div>
    </div>
  );
};

export default DashboardMainContent;
