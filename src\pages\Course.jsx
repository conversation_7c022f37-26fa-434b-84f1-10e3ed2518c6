import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import DataTable from '../components/ui/DataTable';
import '../styles/Dashboard.css';

const Course = () => {
  const navigate = useNavigate();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Note: Authentication is handled by the route protection in App.jsx

  // Fetch courses when component loads
  useEffect(() => {
    const fetchCourses = async () => {
      if (!isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);
        console.log('Fetching courses...');

        // Fetch courses with pagination parameters
        const response = await coursesAPI.getAll({
          offset: 0,
          limit: 50 // Fetch more courses for the course management page
        });

        console.log('Courses fetched successfully:', response);

        // Transform API response to match component expectations
        const transformedCourses = response.data.map(course => ({
          id: course.id,
          title: course.title,
          description: course.description,
          originalPrice: course.originalPrice,
          offerPrice: course.offerPrice,
          price: course.offerPrice || course.originalPrice, // For backward compatibility
          status: course.status,
          tags: course.tags,
          displayPicture: course.displayPicture,
          createdAt: course.createdAt,
          updatedAt: course.updatedAt
        }));

        setCourses(transformedCourses);

      } catch (error) {
        console.error('Failed to fetch courses:', error);
        setError(error.message || 'Failed to load courses');
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [isAuthenticated]);

  // Handle tab change - navigate to different routes
  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      navigate('/dashboard');
    } else if (tabId === 'courses') {
      navigate('/course');
    } else if (tabId === 'profile') {
      navigate('/profile');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCourseClick = (course) => {
    // Admin portal - courses are for management, not reading
    console.log('Course selected for management:', course);
    // TODO: Navigate to course detail/edit page when implemented
    // navigate(`/course/${course.id}`);
  };

  const handleRetry = () => {
    // Trigger a refetch of courses
    window.location.reload();
  };

  // Format price for display
  const formatPrice = (price) => {
    if (!price) return 'N/A';
    return typeof price === 'string' ? price : `₹${price}`;
  };

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return 'Unknown';
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  // Truncate text for display
  const truncateText = (text, maxLength = 80) => {
    if (!text) return 'N/A';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Define table columns configuration
  const columns = [
    {
      key: 'title',
      header: 'Title',
      cellClassName: 'course-title',
      render: (value) => (
        <div className="course-title-content">
          <span className="course-title-text">{value}</span>
        </div>
      )
    },
    {
      key: 'description',
      header: 'Description',
      cellClassName: 'course-description',
      render: (value) => (
        <span title={value}>
          {truncateText(value, 80)}
        </span>
      )
    },
    {
      key: 'originalPrice',
      header: 'Original Price',
      cellClassName: 'course-original-price',
      formatter: formatPrice
    },
    {
      key: 'offerPrice',
      header: 'Offer Price',
      cellClassName: 'course-offer-price',
      formatter: formatPrice
    },
    {
      key: 'status',
      header: 'Status',
      cellClassName: 'course-status',
      render: (value) => (
        <span className={`status-badge status-${value?.toLowerCase() || 'unknown'}`}>
          {formatStatus(value)}
        </span>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      cellClassName: 'course-actions',
      render: (_, course) => (
        <button
          className="action-button view-button"
          onClick={(e) => {
            e.stopPropagation(); // Prevent row click
            handleCourseClick(course);
          }}
          title="Edit course"
        >
          Edit
        </button>
      )
    }
  ];

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab="courses"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <div className="dashboard-main-content">
        <div className="content-container">
          {/* Course page header with Add New Course button */}
          <div className="course-page-header">
            <div className="header-content">
              <h2 className="h2 text-primary">Courses</h2>
              <button
                className="add-course-button"
                onClick={() => navigate('/add-course')}
              >
                <span className="button-icon">+</span>
                Add New Course
              </button>
            </div>
          </div>

          <DataTable
            data={courses}
            loading={loading}
            error={error}
            title="" // Remove title since we have custom header
            columns={columns}
            onRowClick={handleCourseClick}
            onRetry={handleRetry}
            emptyMessage="No courses available at the moment."
            loadingMessage="Loading courses..."
          />
        </div>
      </div>
    </div>
  );
};

export default Course;
