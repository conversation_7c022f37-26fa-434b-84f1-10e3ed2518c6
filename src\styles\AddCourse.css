/* Add Course Page Styles */
.add-course-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-color);
  font-size: var(--body3-size);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}

.back-button:hover {
  background-color: var(--border-color);
  transform: translateX(-2px);
}

.add-course-header h2 {
  margin: 0;
}

/* Form Container */
.add-course-form-container {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  max-width: 800px;
}

.add-course-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-label {
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  margin-bottom: 4px;
}

.required {
  color: #D32F2F;
}

/* Form Inputs */
.form-input,
.form-textarea,
.form-select {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: var(--body3-size);
  font-family: var(--font-family-default);
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--text-color);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #D32F2F;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  font-size: var(--caption-size);
  color: var(--secondary-text-color);
  margin-top: 4px;
}

/* Error Messages */
.error-message {
  font-size: var(--caption-size);
  color: #D32F2F;
  margin-top: 4px;
}

.form-error {
  padding: 12px 16px;
  background-color: #FFEBEE;
  border: 1px solid #FFCDD2;
  border-radius: 8px;
  color: #D32F2F;
  font-size: var(--body3-size);
}

/* File Upload */
.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background-color: #FAFAFA;
}

.file-upload-area:hover {
  border-color: var(--text-color);
  background-color: #F5F5F5;
}

.file-upload-area.drag-active {
  border-color: var(--text-color);
  background-color: #F0F0F0;
  transform: scale(1.02);
}

.file-upload-area.error {
  border-color: #D32F2F;
  background-color: #FFEBEE;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  pointer-events: none;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.file-upload-content p {
  margin: 0;
  font-size: var(--body3-size);
  color: var(--text-color);
}

.file-upload-content small {
  font-size: var(--caption-size);
  color: var(--secondary-text-color);
}

/* File Selected State */
.file-selected {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  pointer-events: auto;
}

.file-icon {
  font-size: 20px;
}

.file-name {
  flex: 1;
  font-size: var(--body3-size);
  color: var(--text-color);
  text-align: left;
}

.file-remove {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--secondary-text-color);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.file-remove:hover {
  background-color: #FFEBEE;
  color: #D32F2F;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
  margin-top: 8px;
}

.cancel-button,
.submit-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.cancel-button {
  background-color: var(--bg-color);
  color: var(--text-color);
}

.cancel-button:hover {
  background-color: var(--border-color);
}

.submit-button {
  background-color: var(--text-color);
  color: var(--bg-color);
  border-color: var(--text-color);
}

.submit-button:hover {
  background-color: var(--secondary-text-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.submit-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .add-course-form-container {
    padding: 24px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .cancel-button,
  .submit-button {
    width: 100%;
    justify-content: center;
  }
  
  .file-upload-area {
    padding: 24px 16px;
  }
}

@media (max-width: 480px) {
  .add-course-form-container {
    padding: 16px;
  }
  
  .add-course-form {
    gap: 20px;
  }
  
  .file-upload-area {
    padding: 20px 12px;
  }
  
  .upload-icon {
    font-size: 24px;
  }
}
