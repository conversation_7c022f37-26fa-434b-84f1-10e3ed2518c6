import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { LoadingSpinner } from '../components/common';

/**
 * PrivateRoute component - Only accessible when authenticated
 * Redirects to login if user is not authenticated
 */
const PrivateRoute = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, isLoading, token, initializeAuth } = useAuthStore();

  useEffect(() => {
    console.log('[PrivateRoute] Auth state:', {
      isAuthenticated,
      hasToken: !!token,
      isLoading,
      path: location.pathname
    });
  }, [isAuthenticated, isLoading, token, location.pathname]);

  // Show loading while auth is being determined
  if (isLoading) {
    console.log('[PrivateRoute] Loading auth state...');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  // If not authenticated, redirect to login with current path
  if (!isAuthenticated) {
    console.log('[PrivateRoute] Not authenticated, redirecting to login', {
      from: location.pathname
    });
    return <Navigate to={`/login?redirect=${encodeURIComponent(location.pathname)}`} replace />;
  }

  console.log('[PrivateRoute] Rendering protected route:', location.pathname);
  return children;
};

export default PrivateRoute;
