import { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { LoadingSpinner } from '../components/common';

/**
 * PrivateRoute component - Only accessible when authenticated
 * Redirects to login if user is not authenticated
 */
const PrivateRoute = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, isLoading, token } = useAuthStore();
  const [authCheckComplete, setAuthCheckComplete] = useState(false);

  useEffect(() => {
    console.log('[PrivateRoute] Auth state:', {
      isAuthenticated,
      hasToken: !!token,
      isLoading,
      path: location.pathname
    });

    // Add a small delay to ensure auth state is stable
    const timer = setTimeout(() => {
      setAuthCheckComplete(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [isAuthenticated, isLoading, token, location.pathname]);

  // Show loading while auth is being determined OR if auth state is undefined OR auth check not complete
  if (isLoading || isAuthenticated === undefined || !authCheckComplete) {
    console.log('[PrivateRoute] Loading auth state or auth undefined or check incomplete...');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  // If explicitly not authenticated (false, not undefined), redirect to login
  if (isAuthenticated === false) {
    console.log('[PrivateRoute] Not authenticated, redirecting to login', {
      from: location.pathname
    });
    return <Navigate to={`/login?redirect=${encodeURIComponent(location.pathname)}`} replace />;
  }

  // If we have a token but isAuthenticated is not explicitly true, show loading
  if (token && isAuthenticated !== true) {
    console.log('[PrivateRoute] Have token but auth state unclear, showing loading...');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Verifying authentication...</span>
      </div>
    );
  }

  console.log('[PrivateRoute] Rendering protected route:', location.pathname);
  return children;
};

export default PrivateRoute;
