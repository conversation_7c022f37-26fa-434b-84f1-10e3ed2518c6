/* DashboardMainContent Component Styles */
.dashboard-main-content {
  flex: 1;
  margin-left: 180px; /* Reduced from 240px */
  padding: 24px; /* Reduced from 32px */
  overflow-y: auto;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Text Color Utilities */
.text-primary {
  color: var(--text-color);
}

.text-secondary {
  color: var(--secondary-text-color);
}

/* Dashboard Home Content */
.dashboard-home {
  padding: 24px 0;
}

.dashboard-welcome {
  margin-bottom: 32px;
  text-align: center;
}

.dashboard-welcome h2 {
  margin-bottom: 12px;
}

.dashboard-welcome p {
  max-width: 600px;
  margin: 0 auto;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.stat-card {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-card h3 {
  margin-bottom: 12px;
}

.stat-card p {
  margin-bottom: 16px;
}

.action-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.action-list li {
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
  font-size: var(--body3-size);
}

.action-list li:last-child {
  border-bottom: none;
}

/* Courses Grid - Exactly 3 cards per row */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 0;
  margin: 0;
}

.course-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.text-error {
  color: #dc3545;
  margin-bottom: 16px;
}

.retry-button {
  background-color: var(--primary-color, #007bff);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

/* Empty State */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}



/* Categories Section */
.categories-section {
  background-color: var(--bg-color);
  border-radius: 12px;
  padding: 32px;
  border: 1px solid var(--border-color);
}

/* My Courses Section */
.my-courses-section {
  background-color: var(--bg-color);
  border-radius: 12px;
  padding: 32px;
  border: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-main-content {
    margin-left: 200px;
    padding: 24px 16px;
  }

  .courses-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

@media (max-width: 640px) {
  .dashboard-main-content {
    margin-left: 0;
    padding: 16px;
  }

  .courses-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
