import React from 'react';
import '../../styles/DataTable.css';

const DataTable = ({
  data = [],
  loading = false,
  error = null,
  title = 'Data',
  columns = [],
  onRowClick,
  onRetry,
  emptyMessage = 'No data available at the moment.',
  loadingMessage = 'Loading data...',
  className = '',
  tableClassName = '',
  containerClassName = ''
}) => {
  // Helper function to render cell content based on column configuration
  const renderCellContent = (item, column) => {
    const value = item[column.key];

    if (column.render) {
      return column.render(value, item);
    }

    if (column.formatter) {
      return column.formatter(value);
    }

    return value || 'N/A';
  };

  // Loading state
  if (loading) {
    return (
      <div className={`data-table-page ${className}`}>
        <div className="data-table-header">
          <h2 className="h2 text-primary">{title}</h2>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="body2 text-secondary">{loadingMessage}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`data-table-page ${className}`}>
        <div className="data-table-header">
          <h2 className="h2 text-primary">{title}</h2>
        </div>
        <div className="error-container">
          <p className="body2 text-error">Failed to load {title.toLowerCase()}: {error}</p>
          <button
            className="retry-button"
            onClick={onRetry || (() => window.location.reload())}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className={`data-table-page ${className}`}>
        <div className="data-table-header">
          <h2 className="h2 text-primary">{title}</h2>
        </div>
        <div className="empty-container">
          <p className="body2 text-secondary">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`data-table-page ${className}`}>
      <header className="data-table-header">
        <div className="header-content">
          <h2 className="h2 text-primary">{title}</h2>
        </div>
      </header>

      <div className="data-table-content">
        <div className={`data-table-container ${containerClassName}`}>
          <div className="data-table-wrapper">
            <table className={`data-table ${tableClassName}`}>
              <thead>
                <tr>
                  {columns.map((column, index) => (
                    <th key={column.key || index} className={column.headerClassName || ''}>
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {data.map((item, rowIndex) => (
                  <tr
                    key={item.id || rowIndex}
                    className="data-row"
                    onClick={() => onRowClick && onRowClick(item)}
                    style={{ cursor: onRowClick ? 'pointer' : 'default' }}
                  >
                    {columns.map((column, colIndex) => (
                      <td
                        key={column.key || colIndex}
                        className={column.cellClassName || ''}
                      >
                        {renderCellContent(item, column)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataTable;
