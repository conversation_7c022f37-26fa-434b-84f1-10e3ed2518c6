import React from 'react';
import '../../styles/CoursesTable.css';

const CoursesTable = ({
  courses = [],
  loading = false,
  error = null,
  onCourseClick
}) => {
  // Loading state
  if (loading) {
    return (
      <div className="courses-table-container">
        <div className="courses-table-header">
          <h2 className="h2 text-primary">Courses</h2>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="body2 text-secondary">Loading courses...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="courses-table-container">
        <div className="courses-table-header">
          <h2 className="h2 text-primary">Courses</h2>
        </div>
        <div className="error-container">
          <p className="body2 text-error">Failed to load courses: {error}</p>
          <button
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (!courses || courses.length === 0) {
    return (
      <div className="courses-table-container">
        <div className="courses-table-header">
          <h2 className="h2 text-primary">Courses</h2>
        </div>
        <div className="empty-container">
          <p className="body2 text-secondary">No courses available at the moment.</p>
        </div>
      </div>
    );
  }

  // Format price for display
  const formatPrice = (price) => {
    if (!price) return 'N/A';
    return typeof price === 'string' ? price : `₹${price}`;
  };

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return 'Unknown';
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  // Format tags for display
  const formatTags = (tags) => {
    if (!tags || !Array.isArray(tags) || tags.length === 0) return 'No tags';
    return tags.join(', ');
  };

  // Truncate text for display
  const truncateText = (text, maxLength = 100) => {
    if (!text) return 'N/A';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="courses-page">
      <header className="courses-header">
        <div className="header-content">
          <h2 className="h2 text-primary">Courses</h2>
        </div>
      </header>
      
      <div className="courses-content">
        <div className="courses-table-container">
          <div className="courses-table-wrapper">
            <table className="courses-table">
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Description</th>
                  <th>Original Price</th>
                  <th>Offer Price</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
          <tbody>
            {courses.map((course) => (
              <tr key={course.id} className="course-row">
                <td className="course-title">
                  <div className="course-title-content">
                    <span className="course-title-text">{course.title}</span>
                  </div>
                </td>
                <td className="course-description">
                  <span title={course.description}>
                    {truncateText(course.description, 80)}
                  </span>
                </td>
                <td className="course-original-price">
                  {formatPrice(course.originalPrice)}
                </td>
                <td className="course-offer-price">
                  {formatPrice(course.price)}
                </td>
                <td className="course-status">
                  <span className={`status-badge status-${course.status?.toLowerCase() || 'unknown'}`}>
                    {formatStatus(course.status)}
                  </span>
                </td>
                <td className="course-actions">
                  <button
                    className="action-button view-button"
                    onClick={() => onCourseClick && onCourseClick(course.id)}
                    title="View course details"
                  >
                    Edit
                  </button>
                </td>
              </tr>
            ))}
              </tbody>
            </table>
          </div>
          
         
        </div>
      </div>
    </div>
  );
};

export default CoursesTable;
