import axios from 'axios';

// API Configuration
const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://*************:8096/somayya-academy/api/v1',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
};

// Create axios instance
const apiClient = axios.create({
  baseURL: API_CONFIG.baseUrl,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Skip adding auth header for login requests
    if (config.url.includes('/auth/')) {
      return config;
    }

    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('[API] Adding auth token to request:', config.url);
    } else {
      console.warn('[API] No auth token found for request:', config.url);
    }
    return config;
  },
  (error) => {
    console.error('[API] Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log('[API] Response:', response.config.method?.toUpperCase(), response.config.url, 'Status:', response.status);
    return response;
  },
  (error) => {
    const { config, response } = error;
    const url = config?.url || 'unknown';
    const status = response?.status;
    const message = response?.data?.message || error.message;

    console.error(`[API] Error: ${config?.method?.toUpperCase()} ${url} - Status: ${status} - ${message}`);

    // Handle 401 unauthorized errors
    if (status === 401) {
      // Skip handling for auth endpoints to prevent redirect loops
      if (url.includes('/auth/')) {
        console.log('[API] Auth endpoint error, skipping redirect');
        return Promise.reject(error);
      }

      console.warn('[API] Unauthorized access detected, clearing auth data');
      // Clear auth data
      localStorage.removeItem('auth-token');

      // Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/login')) {
        console.log('[API] Redirecting to login');
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
      }

      return Promise.reject(error);
    }

    // Handle network errors
    if (!response) {
      error.message = 'Network error. Please check your internet connection.';
      console.error('[API] Network error:', error.message);
    }

    // Add server response data to error if available
    if (response?.data) {
      error.responseData = response.data;
    }

    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  /**
   * Sign in to admin portal
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - Encrypted password
   * @returns {Promise} API response with JWT token
   */
  signIn: async (credentials) => {
    const payload = {
      email: credentials.email,
      password: credentials.password, // Already encrypted by auth store
      portalType: 'ADMIN' // Always ADMIN for this portal
    };

    try {
      const response = await apiClient.post('/auth/signin', payload);

      // Log the response for debugging
      console.log('Login API Response:', response.data);

      // Check if login was successful
      if (response.data.statusCode === 200 && response.data.data?.token) {
        return response.data;
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login API Error:', error);

      // Extract error message from response
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Login failed. Please check your credentials.';
      throw new Error(errorMessage);
    }
  },

  /**
   * Sign out user
   * @returns {Promise} API response
   */
  signOut: async () => {
    try {
      const response = await apiClient.post('/auth/signout');
      return response.data;
    } catch (error) {
      // Even if signout fails on server, we should clear local storage
      console.warn('Signout API failed:', error.message);
      return { success: true };
    }
  },


};

// Courses API endpoints
export const coursesAPI = {
  /**
   * Get all courses with pagination
   * @param {Object} params - Query parameters
   * @param {number} params.offset - Offset for pagination
   * @param {number} params.limit - Limit for pagination
   * @returns {Promise} API response with courses data
   */
  getAll: async (params = {}) => {
    try {
      const { offset = 0, limit = 10 } = params;
      const response = await apiClient.get('/courses/getAll', {
        params: { offset, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch courses:', error);
      throw error;
    }
  },

  /**
   * Create a new course
   * @param {Object} courseData - Course data
   * @param {string} courseData.title - Course title
   * @param {string} courseData.description - Course description
   * @param {number} courseData.originalPrice - Original price
   * @param {number} [courseData.offerPrice] - Offer price (optional)
   * @param {string[]} [courseData.tags] - Course tags
   * @param {File} [courseData.displayPicture] - Display picture file
   * @param {string} [courseData.status] - Course status (Active/Inactive)
   * @returns {Promise} API response with created course data
   */
  create: async (courseData) => {
    try {
      // Create FormData for file upload support
      const formData = new FormData();

      // Add text fields
      formData.append('title', courseData.title);
      formData.append('description', courseData.description);
      formData.append('originalPrice', courseData.originalPrice);

      if (courseData.offerPrice) {
        formData.append('offerPrice', courseData.offerPrice);
      }

      if (courseData.tags && courseData.tags.length > 0) {
        formData.append('tags', JSON.stringify(courseData.tags));
      }

      if (courseData.status) {
        formData.append('status', courseData.status);
      }

      // Add file if provided
      if (courseData.displayPicture) {
        formData.append('displayPicture', courseData.displayPicture);
      }

      const response = await apiClient.post('/courses/create', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Failed to create course:', error);
      throw error;
    }
  }
};

// Admin API endpoints (examples for testing authenticated requests)
export const adminAPI = {
  /**
   * Get admin dashboard data
   * @returns {Promise} API response
   */
  getDashboardData: async () => {
    try {
      const response = await apiClient.get('/admin/dashboard');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      throw error;
    }
  },

  /**
   * Get admin profile
   * @returns {Promise} API response
   */
  getProfile: async () => {
    try {
      const response = await apiClient.get('/admin/profile');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch admin profile:', error);
      throw error;
    }
  },

  /**
   * Test authenticated endpoint
   * @returns {Promise} API response
   */
  testAuth: async () => {
    try {
      const response = await apiClient.get('/admin/test');
      return response.data;
    } catch (error) {
      console.error('Auth test failed:', error);
      throw error;
    }
  }
};

// Generic API methods for other endpoints
export const api = {
  get: (url, config = {}) => apiClient.get(url, config),
  post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
  put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
  delete: (url, config = {}) => apiClient.delete(url, config),
  patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
};

export default apiClient;
