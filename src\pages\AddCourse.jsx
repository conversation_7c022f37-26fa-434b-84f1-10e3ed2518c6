import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import '../styles/Dashboard.css';
import '../styles/AddCourse.css';

const AddCourse = () => {
  const navigate = useNavigate();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    originalPrice: '',
    offerPrice: '',
    tags: '',
    displayPicture: null,
    status: 'Active'
  });
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [dragActive, setDragActive] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Handle tab change - navigate to different routes
  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      navigate('/dashboard');
    } else if (tabId === 'courses') {
      navigate('/course');
    } else if (tabId === 'profile') {
      navigate('/profile');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle file input change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    handleFileSelection(file);
  };

  // Handle file selection (from input or drag & drop)
  const handleFileSelection = (file) => {
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          displayPicture: 'Please select a valid image file (JPG, PNG, or WebP)'
        }));
        return;
      }
      
      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          displayPicture: 'File size must be less than 5MB'
        }));
        return;
      }
      
      setFormData(prev => ({
        ...prev,
        displayPicture: file
      }));
      
      // Clear error
      if (errors.displayPicture) {
        setErrors(prev => ({
          ...prev,
          displayPicture: ''
        }));
      }
    }
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle drop
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelection(e.dataTransfer.files[0]);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    
    if (!formData.originalPrice) {
      newErrors.originalPrice = 'Original price is required';
    } else if (isNaN(formData.originalPrice) || parseFloat(formData.originalPrice) <= 0) {
      newErrors.originalPrice = 'Please enter a valid price';
    }
    
    if (formData.offerPrice && (isNaN(formData.offerPrice) || parseFloat(formData.offerPrice) <= 0)) {
      newErrors.offerPrice = 'Please enter a valid offer price';
    }
    
    if (formData.offerPrice && formData.originalPrice && 
        parseFloat(formData.offerPrice) >= parseFloat(formData.originalPrice)) {
      newErrors.offerPrice = 'Offer price must be less than original price';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      // Prepare data for API
      const courseData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        originalPrice: parseFloat(formData.originalPrice),
        status: formData.status
      };
      
      if (formData.offerPrice) {
        courseData.offerPrice = parseFloat(formData.offerPrice);
      }
      
      if (formData.tags.trim()) {
        courseData.tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      }
      
      if (formData.displayPicture) {
        courseData.displayPicture = formData.displayPicture;
      }
      
      console.log('Creating course with data:', courseData);
      
      // Call API to create course
      const response = await coursesAPI.create(courseData);
      
      console.log('Course created successfully:', response);
      
      // Redirect to courses page on success
      navigate('/course');
      
    } catch (error) {
      console.error('Failed to create course:', error);
      setErrors({
        submit: error.message || 'Failed to create course. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab="courses"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <div className="dashboard-main-content">
        <div className="content-container">
          {/* Header with back button */}
          <div className="add-course-header">
            <button
              className="back-button"
              onClick={() => navigate('/course')}
            >
              ← Back to Courses
            </button>
            <h2 className="h2 text-primary">Add New Course</h2>
          </div>

          {/* Course form */}
          <div className="add-course-form-container">
            <form onSubmit={handleSubmit} className="add-course-form">
              {/* Title field */}
              <div className="form-group">
                <label htmlFor="title" className="form-label">
                  Course Title <span className="required">*</span>
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className={`form-input ${errors.title ? 'error' : ''}`}
                  placeholder="Enter course title"
                />
                {errors.title && <span className="error-message">{errors.title}</span>}
              </div>

              {/* Description field */}
              <div className="form-group">
                <label htmlFor="description" className="form-label">
                  Description <span className="required">*</span>
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className={`form-textarea ${errors.description ? 'error' : ''}`}
                  placeholder="Enter course description"
                  rows="4"
                />
                {errors.description && <span className="error-message">{errors.description}</span>}
              </div>

              {/* Price fields */}
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="originalPrice" className="form-label">
                    Original Price (₹) <span className="required">*</span>
                  </label>
                  <input
                    type="number"
                    id="originalPrice"
                    name="originalPrice"
                    value={formData.originalPrice}
                    onChange={handleInputChange}
                    className={`form-input ${errors.originalPrice ? 'error' : ''}`}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                  />
                  {errors.originalPrice && <span className="error-message">{errors.originalPrice}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="offerPrice" className="form-label">
                    Offer Price (₹)
                  </label>
                  <input
                    type="number"
                    id="offerPrice"
                    name="offerPrice"
                    value={formData.offerPrice}
                    onChange={handleInputChange}
                    className={`form-input ${errors.offerPrice ? 'error' : ''}`}
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                  />
                  {errors.offerPrice && <span className="error-message">{errors.offerPrice}</span>}
                </div>
              </div>

              {/* Tags field */}
              <div className="form-group">
                <label htmlFor="tags" className="form-label">
                  Tags
                </label>
                <input
                  type="text"
                  id="tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Enter tags separated by commas (e.g., programming, web development)"
                />
                <small className="form-help">Separate multiple tags with commas</small>
              </div>

              {/* Status field */}
              <div className="form-group">
                <label htmlFor="status" className="form-label">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="form-select"
                >
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                </select>
              </div>

              {/* File upload field */}
              <div className="form-group">
                <label className="form-label">
                  Display Picture
                </label>
                <div
                  className={`file-upload-area ${dragActive ? 'drag-active' : ''} ${errors.displayPicture ? 'error' : ''}`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    id="displayPicture"
                    name="displayPicture"
                    onChange={handleFileChange}
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    className="file-input"
                  />
                  <div className="file-upload-content">
                    {formData.displayPicture ? (
                      <div className="file-selected">
                        <span className="file-icon">📁</span>
                        <span className="file-name">{formData.displayPicture.name}</span>
                        <button
                          type="button"
                          className="file-remove"
                          onClick={() => setFormData(prev => ({ ...prev, displayPicture: null }))}
                        >
                          ×
                        </button>
                      </div>
                    ) : (
                      <>
                        <span className="upload-icon">📤</span>
                        <p>Drag & drop an image here, or click to select</p>
                        <small>Supports: JPG, PNG, WebP (Max 5MB)</small>
                      </>
                    )}
                  </div>
                </div>
                {errors.displayPicture && <span className="error-message">{errors.displayPicture}</span>}
              </div>

              {/* Submit error */}
              {errors.submit && (
                <div className="form-error">
                  {errors.submit}
                </div>
              )}

              {/* Form actions */}
              <div className="form-actions">
                <button
                  type="button"
                  className="cancel-button"
                  onClick={() => navigate('/course')}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="submit-button"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Create Course'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddCourse;
