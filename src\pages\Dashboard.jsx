import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import SideNavBar from '../components/layout/SideNavBar';
import DashboardMainContent from '../components/layout/DashboardMainContent';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  const [activeTab, setActiveTab] = useState('home');

  // Set active tab based on current route
  useEffect(() => {
    if (location.pathname === '/course') {
      setActiveTab('courses');
    } else if (location.pathname === '/dashboard') {
      setActiveTab('home');
    }
  }, [location.pathname]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Handle tab change - navigate to different routes
  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      navigate('/dashboard');
    } else if (tabId === 'courses') {
      navigate('/course');
    } else if (tabId === 'profile') {
      navigate('/profile');
    }
    // Add more navigation logic for future tabs
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab={activeTab}
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <DashboardMainContent
        activeTab={activeTab}
      />
    </div>
  );
};

export default Dashboard;
